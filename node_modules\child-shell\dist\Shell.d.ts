/// <reference types="node" />
import { SpawnOptions } from 'child_process';
import { Readable, Writable } from 'stream';
import Debugger from 'debug';
import { Converters } from './converters';
export declare type StdioStreams = {
    stdin: Writable;
    stdout: Readable;
    stderr: Readable;
};
export declare type Debuggers = {
    comment: Debugger.Debugger;
    command: Debugger.Debugger;
    output: Debugger.Debugger;
};
export declare type ExecutableOptions = {
    [key: string]: boolean | string;
};
export declare type ShellSpawnOptions = Omit<SpawnOptions, 'argv0' | 'stdio' | 'detached' | 'serialization' | 'shell' | 'timeout'>;
export declare type InvocationResult = {
    command: string;
    raw: string;
    hadErrors: boolean;
    startTime: number;
    duration?: number;
    stdout?: Buffer;
    stderr?: Buffer;
};
export declare type ShellOptions = {
    executable?: string;
    executableOptions?: ExecutableOptions;
    spawnOptions?: ShellSpawnOptions;
    inputEncoding?: BufferEncoding;
    outputEncoding?: BufferEncoding;
    debug?: boolean;
    disposeTimeout?: number;
    invocationTimeout?: number;
    throwOnInvocationError?: boolean;
};
export declare type ShellCtor = {
    new (options?: ShellOptions): Shell;
};
export declare type ShellDerived = ShellCtor & typeof Shell;
export declare abstract class Shell {
    private readonly executable;
    private readonly executableOptions;
    private readonly spawnOptions;
    private readonly invocationQueue;
    private readonly debuggers;
    private readonly process;
    private isExited;
    private readonly exitPromise;
    private readonly resultsEncoding;
    private readonly invocationTimeout;
    private readonly throwOnInvocationError;
    protected abstract writeToOutput(input: string): string;
    protected abstract writeToError(input: string): string;
    protected static converters: Converters;
    readonly streams: StdioStreams;
    readonly history: InvocationResult[];
    constructor(options?: ShellOptions);
    private detachStreamFromOutput;
    private readBulkFromOutput;
    protected setExecutable({ executable }: {
        executable?: string;
    }): string;
    private setExecutableOptions;
    private setInvocationQueue;
    private clearInvocationQueue;
    private setDebuggers;
    private setProcessExitListeners;
    private setProcessStdioEncoding;
    private setProcessKillOptions;
    private invokeCommand;
    invoke(command: string): Promise<InvocationResult>;
    dispose(signal?: NodeJS.Signals): Promise<void>;
    private static convertToShell;
    static command(this: ShellDerived, literals: readonly string[], ...expressions: unknown[]): string;
    static invoke(this: ShellDerived, command: string, options?: ShellOptions): Promise<InvocationResult>;
    static $(this: ShellDerived, literals: readonly string[], ...expressions: unknown[]): Promise<InvocationResult>;
    static $$(this: ShellDerived, literals: readonly string[], ...expressions: unknown[]): (options?: ShellOptions | undefined) => Promise<InvocationResult>;
}
