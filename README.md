# Windows MCP Server

A Model Context Protocol (MCP) server that provides Windows system integration capabilities for <PERSON>op.

## Features

This MCP server provides the following tools:

- **run_powershell**: Execute PowerShell commands on Windows
- **run_cmd**: Execute Command Prompt commands on Windows  
- **list_directory**: List contents of a directory
- **read_file**: Read contents of a text file
- **write_file**: Write content to a file
- **get_system_info**: Get Windows system information

## Installation

1. Make sure you have Node.js installed on your Windows system
2. The server is already built and ready to use in the `dist` directory

## Configuration

To use this MCP server with Claude Desktop, you need to add it to your Claude Desktop configuration:

### Option 1: Manual Configuration

1. Open Claude Desktop
2. Go to Settings → Developer → MCP Servers
3. Add a new server with these settings:
   - **Name**: `windows-mcp`
   - **Command**: `node`
   - **Arguments**: `["C:\\windowsmcp\\dist\\index.js"]`

### Option 2: Configuration File

1. Locate your Claude Desktop configuration file:
   - Windows: `%APPDATA%\Claude\claude_desktop_config.json`

2. Add the following configuration to the file (or create it if it doesn't exist):

```json
{
  "mcpServers": {
    "windows-mcp": {
      "command": "node",
      "args": ["C:\\windowsmcp\\dist\\index.js"],
      "env": {}
    }
  }
}
```

3. Restart Claude Desktop

## Usage

Once configured, you can use the Windows MCP server tools in your conversations with Claude. For example:

- "List the files in my Documents folder"
- "Run a PowerShell command to check system information"
- "Create a new text file with some content"
- "Read the contents of a specific file"

## Security Note

This MCP server provides direct access to your Windows system through PowerShell and Command Prompt. Only use it in trusted environments and be cautious about the commands you ask Claude to execute.

## Development

To modify the server:

1. Edit the TypeScript source in `src/index.ts`
2. Run `npm run build` to compile
3. Restart Claude Desktop to use the updated server

## Troubleshooting

- Make sure Node.js is installed and accessible from the command line
- Verify the path to the server in your Claude Desktop configuration
- Check the Claude Desktop logs for any error messages
- Ensure you have the necessary permissions to execute the commands you're trying to run
