export declare type JavaScriptTypes = 'undefined' | 'null' | 'boolean' | 'buffer' | 'number' | 'string' | 'arguments' | 'object' | 'date' | 'array' | 'regexp' | 'error' | 'function' | 'generatorfunction' | 'symbol' | 'map' | 'weakmap' | 'set' | 'weakset' | 'int8array' | 'uint8array' | 'uint8clampedarray' | 'int16array' | 'uint16array' | 'int32array' | 'uint32array' | 'float32array' | 'float64array';
export declare type Converter = (object: unknown, convert: ConvertFn) => string;
export declare type Converters = Map<JavaScriptTypes, Converter>;
export declare type ConvertFn = (object: unknown, converters?: Converters) => string;
export declare const SHELL_CONVERTERS: Converters;
