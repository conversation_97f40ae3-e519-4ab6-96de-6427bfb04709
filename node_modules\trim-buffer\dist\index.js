"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.trimBuffer = exports.trimBufferEnd = exports.trimBufferStart = exports.isWhitespaceByte = void 0;
const bn = '\n'.charCodeAt(0);
const br = '\r'.charCodeAt(0);
const space = ' '.charCodeAt(0);
const isWhitespaceByte = (byte) => byte === bn || byte === br || byte === space;
exports.isWhitespaceByte = isWhitespaceByte;
const trimBufferStart = (buf) => {
    let start = 0;
    for (let i = 0; i <= buf.length; i++) {
        if (!(0, exports.isWhitespaceByte)(buf[i])) {
            start = i;
            break;
        }
    }
    return buf.slice(start);
};
exports.trimBufferStart = trimBufferStart;
const trimBufferEnd = (buf) => {
    let end = buf.length;
    for (let i = buf.length - 1; i >= 0; i--) {
        if (!(0, exports.isWhitespaceByte)(buf[i])) {
            end = i;
            break;
        }
    }
    return buf.slice(0, end + 1);
};
exports.trimBufferEnd = trimBufferEnd;
const trimBuffer = (buf) => {
    return (0, exports.trimBufferStart)((0, exports.trimBufferEnd)(buf));
};
exports.trimBuffer = trimBuffer;
//# sourceMappingURL=index.js.map