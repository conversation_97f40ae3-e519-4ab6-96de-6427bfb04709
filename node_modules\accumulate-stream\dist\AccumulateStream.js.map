{"version": 3, "file": "AccumulateStream.js", "sourceRoot": "", "sources": ["../lib/AccumulateStream.ts"], "names": [], "mappings": ";;;;;;AAAA,mCAAsD;AACtD,4CAAoB;AACpB,kDAA0B;AAgB1B,MAAa,gBAAiB,SAAQ,kBAAS;IAQ7C,YAAY,UAAmC,EAAE;;QAC/C,KAAK,CAAC;YACJ,aAAa,EAAE,IAAI;YACnB,UAAU,EAAE,KAAK;YACjB,WAAW,EAAE,IAAI;YACjB,SAAS,EAAE,IAAI;YACf,aAAa,EAAE,KAAK;SACrB,CAAC,CAAC;QACH,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,KAAK,EAAE,CAAC;QAEb,IAAI,MAAA,IAAI,CAAC,OAAO,0CAAE,QAAQ,EAAE;YAC1B,IAAI,CAAC,QAAQ,GAAG,WAAW,CAAC,GAAG,EAAE;gBAC/B,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;gBAC3B,IAAI,CAAC,KAAK,EAAE,CAAC;YACf,CAAC,EAAE,IAAA,YAAE,EAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;SAC/B;IACH,CAAC;IAEO,KAAK;QACX,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC9B,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;IACzB,CAAC;IAEO,UAAU,CAAC,KAAa;QAC9B,MAAM,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAC1D,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE,eAAe,CAAC,CAAC;QACnE,IAAI,CAAC,aAAa,IAAI,CAAC,CAAC;IAC1B,CAAC;IAEO,KAAK;QACX,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;QAC5B,IAAI,CAAC,KAAK,EAAE,CAAC;IACf,CAAC;IAEO,SAAS,CAAC,KAA6B,EAAE,IAAI,GAAG,EAAE;QACxD,IAAI,CAAC,IAAI,CAAC,KAAK,kBACb,MAAM,EAAE,IAAI,CAAC,SAAS,EAAE,IACrB,IAAI,EACP,CAAC;IACL,CAAC;IAED,UAAU,CAAC,KAAa,EAAE,QAAwB,EAAE,EAAqB;;QACvE,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QACvB,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;QAEnC,MAAM,WAAW,GAAG,CAAA,MAAA,IAAI,CAAC,OAAO,0CAAE,KAAK,KAAI,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC,aAAa,CAAC;QACpF,IAAI,WAAW,EAAE;YACf,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;SACzB;QAED,MAAM,UAAU,GAAG,CAAA,MAAA,IAAI,CAAC,OAAO,0CAAE,IAAI,KAAI,IAAA,eAAK,EAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;QAC5F,IAAI,UAAU,EAAE;YACd,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC;SAC1D;QAED,MAAM,YAAY,GAAG,CAAA,MAAA,IAAI,CAAC,OAAO,0CAAE,MAAM,KAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QACvF,IAAI,YAAY,EAAE;YAChB,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;SAC1B;QAED,MAAM,YAAY,GAChB,CAAA,MAAA,MAAA,IAAI,CAAC,OAAO,0CAAE,MAAM,0CAAE,KAAK;aAC3B,MAAA,MAAA,IAAI,CAAC,OAAO,0CAAE,MAAM,0CAAE,MAAM,CAAA;aAC5B,MAAA,MAAA,MAAA,IAAI,CAAC,OAAO,0CAAE,MAAM,0CAAE,MAAM,0CAAE,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAA,CAAC;QAC5D,IAAI,YAAY,EAAE;YAChB,IAAI,CAAC,SAAS,CAAC,MAAA,MAAA,IAAI,CAAC,OAAO,0CAAE,MAAM,0CAAE,KAAe,CAAC,CAAC;SACvD;QAED,MAAM,MAAM,GAAG,UAAU,IAAI,WAAW,IAAI,YAAY,IAAI,YAAY,CAAC;QACzE,IAAI,MAAM,EAAE;YACV,IAAI,CAAC,KAAK,EAAE,CAAC;SACd;QAED,OAAO,EAAE,EAAE,CAAC;IACd,CAAC;IAED,MAAM,CAAC,EAAqB;;QAC1B,IAAI,CAAA,MAAA,IAAI,CAAC,OAAO,0CAAE,SAAS,KAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE;YAC9C,IAAI,CAAC,KAAK,EAAE,CAAC;SACd;QAED,OAAO,EAAE,EAAE,CAAC;IACd,CAAC;IAED,MAAM,CAAC,EAAqB;QAC1B,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SAC9B;QAED,OAAO,EAAE,EAAE,CAAC;IACd,CAAC;IAEM,OAAO;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,CAAC;IAClC,CAAC;IAEM,SAAS;QACd,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;CACF;AA5GD,4CA4GC"}