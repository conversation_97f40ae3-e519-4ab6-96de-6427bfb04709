# `node-powershell`

> Node.js binding for PowerShell

[![NPM Version](https://img.shields.io/npm/v/node-powershell.svg?style=flat-square)](https://www.npmjs.com/package/node-powershell) [![NPM Downloads](https://img.shields.io/npm/dt/node-powershell.svg?style=flat-square)](https://npm-stat.com/charts.html?package=node-powershell)

## Installation

```bash
$ npm i -S node-powershell
$ yarn add node-powershell
```

## Usage

```javascript
import { PowerShell } from 'node-powershell';
PowerShell.$`echo "hello from PowerShell"`;
```

> See **child-shell** [documentation](https:/<>) and [playground](https://github.com/rannn505/child-shell/blob/master/playground/index.ts) for more use cases

## Resources

- [child-shell Documentation](https:/<>)
- [PowerShell Documentation](https://microsoft.com/PowerShell)
- [PowerShell Repo](https://github.com/PowerShell/PowerShell)
- [Node.js Documentation](https://nodejs.org/en/docs/)
