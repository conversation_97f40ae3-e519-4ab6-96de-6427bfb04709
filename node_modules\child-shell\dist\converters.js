"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SHELL_CONVERTERS = void 0;
const emptyConverter = () => '';
const primitiveConverter = (object) => String(object);
const stringConverter = (object) => {
    const hasDoubleQuotes = object.includes('"');
    if (hasDoubleQuotes) {
        return `'${object}'`;
    }
    return `"${object}"`;
};
const objectConverter = (object) => JSON.stringify(object);
const arrayConverter = (object, convert) => object.map((el) => convert(el)).join(',');
exports.SHELL_CONVERTERS = new Map([
    ['undefined', emptyConverter],
    ['null', emptyConverter],
    ['boolean', primitiveConverter],
    ['number', primitiveConverter],
    ['string', stringConverter],
    ['object', objectConverter],
    ['date', primitiveConverter],
    ['array', arrayConverter],
    ['regexp', stringConverter],
    ['symbol', primitiveConverter],
]);
//# sourceMappingURL=converters.js.map