"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.InvocationError = exports.ProcessError = exports.BaseError = void 0;
class BaseError extends Error {
    constructor(message = '') {
        super(message);
        this.name = this.constructor.name;
        Error.captureStackTrace(this, this.constructor);
    }
}
exports.BaseError = BaseError;
class ProcessError extends BaseError {
    constructor(process, originalError) {
        const { pid, exitCode, signalCode } = process;
        const message = `Shell process${pid ? ` ${pid}` : ''} exited.\n${originalError === null || originalError === void 0 ? void 0 : originalError.message}`;
        super(message);
        this.originalError = originalError;
        this.exitCode = exitCode;
        this.signalCode = signalCode;
    }
}
exports.ProcessError = ProcessError;
class InvocationError extends BaseError {
}
exports.InvocationError = InvocationError;
//# sourceMappingURL=errors.js.map