#!/usr/bin/env node
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { CallToolRequestSchema, ErrorCode, ListToolsRequestSchema, McpError, } from '@modelcontextprotocol/sdk/types.js';
import { exec } from 'child_process';
import { promisify } from 'util';
import * as fs from 'fs/promises';
const execAsync = promisify(exec);
class WindowsMCPServer {
    server;
    constructor() {
        this.server = new Server({
            name: 'windows-mcp-server',
            version: '1.0.0',
        }, {
            capabilities: {
                tools: {},
            },
        });
        this.setupToolHandlers();
        this.setupErrorHandling();
    }
    setupErrorHandling() {
        this.server.onerror = (error) => console.error('[MCP Error]', error);
        process.on('SIGINT', async () => {
            await this.server.close();
            process.exit(0);
        });
    }
    setupToolHandlers() {
        this.server.setRequestHandler(ListToolsRequestSchema, async () => ({
            tools: [
                {
                    name: 'run_powershell',
                    description: 'Execute PowerShell commands on Windows',
                    inputSchema: {
                        type: 'object',
                        properties: {
                            command: {
                                type: 'string',
                                description: 'PowerShell command to execute',
                            },
                            timeout: {
                                type: 'number',
                                description: 'Timeout in milliseconds (default: 30000)',
                                default: 30000,
                            },
                        },
                        required: ['command'],
                    },
                },
                {
                    name: 'run_cmd',
                    description: 'Execute Command Prompt commands on Windows',
                    inputSchema: {
                        type: 'object',
                        properties: {
                            command: {
                                type: 'string',
                                description: 'CMD command to execute',
                            },
                            timeout: {
                                type: 'number',
                                description: 'Timeout in milliseconds (default: 30000)',
                                default: 30000,
                            },
                        },
                        required: ['command'],
                    },
                },
                {
                    name: 'list_directory',
                    description: 'List contents of a directory',
                    inputSchema: {
                        type: 'object',
                        properties: {
                            path: {
                                type: 'string',
                                description: 'Directory path to list',
                            },
                        },
                        required: ['path'],
                    },
                },
                {
                    name: 'read_file',
                    description: 'Read contents of a text file',
                    inputSchema: {
                        type: 'object',
                        properties: {
                            path: {
                                type: 'string',
                                description: 'File path to read',
                            },
                            encoding: {
                                type: 'string',
                                description: 'File encoding (default: utf8)',
                                default: 'utf8',
                            },
                        },
                        required: ['path'],
                    },
                },
                {
                    name: 'write_file',
                    description: 'Write content to a file',
                    inputSchema: {
                        type: 'object',
                        properties: {
                            path: {
                                type: 'string',
                                description: 'File path to write to',
                            },
                            content: {
                                type: 'string',
                                description: 'Content to write',
                            },
                            encoding: {
                                type: 'string',
                                description: 'File encoding (default: utf8)',
                                default: 'utf8',
                            },
                        },
                        required: ['path', 'content'],
                    },
                },
                {
                    name: 'get_system_info',
                    description: 'Get Windows system information',
                    inputSchema: {
                        type: 'object',
                        properties: {},
                    },
                },
            ],
        }));
        this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
            const { name, arguments: args } = request.params;
            if (!args) {
                throw new McpError(ErrorCode.InvalidParams, 'Missing arguments');
            }
            try {
                switch (name) {
                    case 'run_powershell':
                        return await this.runPowerShell(args.command, args.timeout || 30000);
                    case 'run_cmd':
                        return await this.runCmd(args.command, args.timeout || 30000);
                    case 'list_directory':
                        return await this.listDirectory(args.path);
                    case 'read_file':
                        return await this.readFile(args.path, args.encoding || 'utf8');
                    case 'write_file':
                        return await this.writeFile(args.path, args.content, args.encoding || 'utf8');
                    case 'get_system_info':
                        return await this.getSystemInfo();
                    default:
                        throw new McpError(ErrorCode.MethodNotFound, `Unknown tool: ${name}`);
                }
            }
            catch (error) {
                const errorMessage = error instanceof Error ? error.message : String(error);
                throw new McpError(ErrorCode.InternalError, `Tool execution failed: ${errorMessage}`);
            }
        });
    }
    async runPowerShell(command, timeout) {
        try {
            const { stdout, stderr } = await execAsync(`powershell.exe -Command "${command}"`, {
                timeout,
                encoding: 'utf8',
            });
            return {
                content: [
                    {
                        type: 'text',
                        text: `PowerShell Output:\n${stdout}${stderr ? `\nErrors:\n${stderr}` : ''}`,
                    },
                ],
            };
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            return {
                content: [
                    {
                        type: 'text',
                        text: `PowerShell Error: ${errorMessage}`,
                    },
                ],
                isError: true,
            };
        }
    }
    async runCmd(command, timeout) {
        try {
            const { stdout, stderr } = await execAsync(`cmd.exe /c "${command}"`, {
                timeout,
                encoding: 'utf8',
            });
            return {
                content: [
                    {
                        type: 'text',
                        text: `CMD Output:\n${stdout}${stderr ? `\nErrors:\n${stderr}` : ''}`,
                    },
                ],
            };
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            return {
                content: [
                    {
                        type: 'text',
                        text: `CMD Error: ${errorMessage}`,
                    },
                ],
                isError: true,
            };
        }
    }
    async listDirectory(dirPath) {
        try {
            const entries = await fs.readdir(dirPath, { withFileTypes: true });
            const items = entries.map(entry => ({
                name: entry.name,
                type: entry.isDirectory() ? 'directory' : 'file',
                isDirectory: entry.isDirectory(),
                isFile: entry.isFile(),
            }));
            return {
                content: [
                    {
                        type: 'text',
                        text: `Directory listing for ${dirPath}:\n${JSON.stringify(items, null, 2)}`,
                    },
                ],
            };
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            return {
                content: [
                    {
                        type: 'text',
                        text: `Directory listing error: ${errorMessage}`,
                    },
                ],
                isError: true,
            };
        }
    }
    async readFile(filePath, encoding) {
        try {
            const content = await fs.readFile(filePath, encoding);
            return {
                content: [
                    {
                        type: 'text',
                        text: content,
                    },
                ],
            };
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            return {
                content: [
                    {
                        type: 'text',
                        text: `File read error: ${errorMessage}`,
                    },
                ],
                isError: true,
            };
        }
    }
    async writeFile(filePath, content, encoding) {
        try {
            await fs.writeFile(filePath, content, encoding);
            return {
                content: [
                    {
                        type: 'text',
                        text: `Successfully wrote to ${filePath}`,
                    },
                ],
            };
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            return {
                content: [
                    {
                        type: 'text',
                        text: `File write error: ${errorMessage}`,
                    },
                ],
                isError: true,
            };
        }
    }
    async getSystemInfo() {
        try {
            const { stdout } = await execAsync('systeminfo', { encoding: 'utf8' });
            return {
                content: [
                    {
                        type: 'text',
                        text: `System Information:\n${stdout}`,
                    },
                ],
            };
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            return {
                content: [
                    {
                        type: 'text',
                        text: `System info error: ${errorMessage}`,
                    },
                ],
                isError: true,
            };
        }
    }
    async run() {
        const transport = new StdioServerTransport();
        await this.server.connect(transport);
        console.error('Windows MCP server running on stdio');
    }
}
const server = new WindowsMCPServer();
server.run().catch(console.error);
//# sourceMappingURL=index.js.map