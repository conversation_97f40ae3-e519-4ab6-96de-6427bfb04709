/// <reference types="node" />
import { ChildProcess } from 'child_process';
export declare class BaseError extends Error {
    constructor(message?: string);
}
export declare class ProcessError extends BaseError {
    readonly originalError?: Error;
    readonly exitCode: ChildProcess['exitCode'];
    readonly signalCode: ChildProcess['signalCode'];
    readonly message: string;
    constructor(process: ChildProcess, originalError?: Error);
}
export declare class InvocationError extends BaseError {
}
