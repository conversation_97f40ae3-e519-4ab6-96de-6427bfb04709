"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PowerShell = exports.PSExecutableType = void 0;
const os_1 = require("os");
const is_wsl_1 = __importDefault(require("is-wsl"));
const child_shell_1 = require("child-shell");
var PSExecutableType;
(function (PSExecutableType) {
    PSExecutableType["PowerShellWin"] = "powershell";
    PSExecutableType["PowerShellCore"] = "pwsh";
    PSExecutableType["PowerShellCorePreview"] = "pwsh-preview";
})(PSExecutableType = exports.PSExecutableType || (exports.PSExecutableType = {}));
const isWin = (0, os_1.platform)() === 'win32' || is_wsl_1.default;
const nullConverter = () => '$Null';
const booleanConverter = (object) => (object ? '$True' : '$False');
const objectConverter = (object) => `@${JSON.stringify(object).replace(/:/g, '=').replace(/,/g, ';')}`;
const dateConverter = (object) => object.toLocaleString();
const PS_CONVERTERS = new Map([
    ['null', nullConverter],
    ['boolean', booleanConverter],
    ['object', objectConverter],
    ['date', dateConverter],
]);
class PowerShell extends child_shell_1.Shell {
    constructor(options = {}) {
        super(Object.assign(Object.assign({}, options), { executableOptions: Object.assign(Object.assign({ '-NoLogo': true }, options.executableOptions), { '-NoExit': true, '-Command': '-' }) }));
    }
    setExecutable({ pwsh = false, pwshPrev = false, executable, }) {
        const { PowerShellWin, PowerShellCore, PowerShellCorePreview } = PSExecutableType;
        if (process.env.NODE_POWERSHELL) {
            return process.env.NODE_POWERSHELL;
        }
        if (pwsh) {
            return PowerShellCore;
        }
        if (pwshPrev) {
            return PowerShellCorePreview;
        }
        if (!executable) {
            return !isWin ? PowerShellCore : PowerShellWin;
        }
        switch (executable) {
            case PowerShellWin:
                return PowerShellWin;
            case PowerShellCore:
                return PowerShellCore;
            case PowerShellCorePreview:
                return PowerShellCore;
            default:
                return super.setExecutable({ executable });
        }
    }
    writeToOutput(input) {
        return `[Console]::Out.WriteLine("${input}")`;
    }
    writeToError(input) {
        return `[Console]::Error.WriteLine("${input}")`;
    }
}
exports.PowerShell = PowerShell;
PowerShell.converters = new Map([...child_shell_1.Shell.converters, ...PS_CONVERTERS]);
//# sourceMappingURL=PowerShell.js.map