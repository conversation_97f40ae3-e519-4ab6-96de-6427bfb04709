import { ExecutableOptions, ShellOptions, Shell, Converter } from 'child-shell';
export declare enum PSExecutableType {
    PowerShellWin = "powershell",
    PowerShellCore = "pwsh",
    PowerShellCorePreview = "pwsh-preview"
}
export declare type PSExecutableOptions = ExecutableOptions & Partial<{
    '-PSConsoleFile': string;
    '-Version': '2.0' | '3.0';
    '-NoLogo': boolean;
    '-NoExit': boolean;
    '-Sta': boolean;
    '-Mta': boolean;
    '-NoProfile': boolean;
    '-NonInteractive': boolean;
    '-InputFormat': 'Text' | 'XML';
    '-OutputFormat': 'Text' | 'XML';
    '-WindowStyle': string;
    '-ConfigurationName': string;
    '-ExecutionPolicy': 'AllSigned' | 'Bypass' | 'Default' | 'RemoteSigned' | 'Restricted' | 'Undefined' | 'Unrestricted' | string;
}>;
export declare type PowerShellOptions = ShellOptions & {
    pwsh?: boolean;
    pwshPrev?: boolean;
    executable?: PSExecutableType | string;
    executableOptions?: PSExecutableOptions;
};
export declare class PowerShell extends Shell {
    static converters: Map<import("child-shell").JavaScriptTypes, Converter>;
    constructor(options?: PowerShellOptions);
    protected setExecutable({ pwsh, pwshPrev, executable, }: {
        pwsh?: boolean;
        pwshPrev?: boolean;
        executable?: PSExecutableType;
    }): string;
    protected writeToOutput(input: string): string;
    protected writeToError(input: string): string;
}
