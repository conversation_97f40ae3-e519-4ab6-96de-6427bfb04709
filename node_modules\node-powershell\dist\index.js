"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProcessError = exports.InvocationError = void 0;
var child_shell_1 = require("child-shell");
Object.defineProperty(exports, "InvocationError", { enumerable: true, get: function () { return child_shell_1.InvocationError; } });
Object.defineProperty(exports, "ProcessError", { enumerable: true, get: function () { return child_shell_1.ProcessError; } });
__exportStar(require("./PowerShell"), exports);
//# sourceMappingURL=index.js.map